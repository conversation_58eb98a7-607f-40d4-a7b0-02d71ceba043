import React from "react";
import { DroppedItem } from "@/types";
import { AlertCircle, FileText, Image, Video, Link, FileAudio, FileArchive, Code, Boxes, Gamepad2, ListChecks, FormInput, Upload, FileQuestion, CheckSquare, Star, Calendar, Clock, Timer, Phone, Hash, PenTool, Loader2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useImagePresignedUrl, useVideoPresignedUrl, useAudioPresignedUrl, useAttachmentPresignedUrl } from "@/hooks/usePresignedUrl";

interface ComponentPreviewProps {
  item: DroppedItem;
  isPreview?: boolean;
}

const ComponentPreview: React.FC<ComponentPreviewProps> = ({ item, isPreview = false }) => {
  const { type, data } = item;

  // Get presigned URLs for different file types
  const imageUrl = useImagePresignedUrl(data);
  const videoUrl = useVideoPresignedUrl(data);
  const audioUrl = useAudioPresignedUrl(data);
  const attachmentUrl = useAttachmentPresignedUrl(data);

  // Render preview based on component type
  const renderPreview = () => {
    switch (type) {
      case "text":
        return (
          <div className="text-sm">
            {(data as any).content || (
              <span className="text-muted-foreground italic">No content added yet</span>
            )}
          </div>
        );

      case "image":
        if (imageUrl.isLoading) {
          return (
            <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <Loader2 className="h-8 w-8 mb-2 opacity-50 animate-spin" />
                <span className="text-xs">Loading image...</span>
              </div>
            </div>
          );
        }

        if (imageUrl.error) {
          return (
            <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <Image className="h-8 w-8 mb-2 opacity-50" />
                <span className="text-xs">Failed to load image</span>
              </div>
            </div>
          );
        }

        return imageUrl.url ? (
          <div className="relative aspect-video bg-slate-100 dark:bg-slate-700 rounded-md overflow-hidden flex items-center justify-center">
            <img
              src={imageUrl.url}
              alt={(data as any).alt || "Image"}
              className="max-h-full max-w-full object-contain"
            />
          </div>
        ) : (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <Image className="h-8 w-8 mb-2 opacity-50" />
              <span className="text-xs">No image uploaded</span>
            </div>
          </div>
        );

      case "video":
        if (videoUrl.isLoading) {
          return (
            <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <Loader2 className="h-8 w-8 mb-2 opacity-50 animate-spin" />
                <span className="text-xs">Loading video...</span>
              </div>
            </div>
          );
        }

        if (videoUrl.error) {
          return (
            <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <Video className="h-8 w-8 mb-2 opacity-50" />
                <span className="text-xs">Failed to load video</span>
              </div>
            </div>
          );
        }

        return videoUrl.url ? (
          <div className="relative aspect-video bg-slate-100 dark:bg-slate-700 rounded-md overflow-hidden">
            <video
              src={videoUrl.url}
              controls
              className="w-full h-full"
            />
          </div>
        ) : (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <Video className="h-8 w-8 mb-2 opacity-50" />
              <span className="text-xs">No video uploaded</span>
            </div>
          </div>
        );

      case "youtube":
        return (data as any).videoId ? (
          <div className="relative aspect-video bg-slate-100 dark:bg-slate-700 rounded-md overflow-hidden">
            <iframe
              width="100%"
              height="100%"
              src={`https://www.youtube.com/embed/${(data as any).videoId}`}
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
        ) : (
          <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <Video className="h-8 w-8 mb-2 opacity-50 text-red-500" />
              <span className="text-xs">No YouTube video added</span>
            </div>
          </div>
        );

      case "weblink":
        return (data as any).url ? (
          <div className="flex items-center p-3 border rounded-md">
            {(data as any).thumbnail ? (
              <img
                src={(data as any).thumbnail}
                alt="Thumbnail"
                className="w-12 h-12 object-cover rounded mr-3"
              />
            ) : (
              <div className="w-12 h-12 bg-slate-100 rounded flex items-center justify-center mr-3">
                <Link className="h-6 w-6 text-slate-400" />
              </div>
            )}
            <div>
              <div className="font-medium">
                {(data as any).title || "Untitled Link"}
              </div>
              <div className="text-xs text-blue-500 truncate max-w-[300px]">
                {(data as any).url}
              </div>
            </div>
          </div>
        ) : (
          <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <Link className="h-6 w-6 mb-1 opacity-50" />
              <span className="text-xs">No web link added</span>
            </div>
          </div>
        );

      case "audio":
        if (audioUrl.isLoading) {
          return (
            <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <Loader2 className="h-6 w-6 mb-1 opacity-50 animate-spin" />
                <span className="text-xs">Loading audio...</span>
              </div>
            </div>
          );
        }

        if (audioUrl.error) {
          return (
            <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <FileAudio className="h-6 w-6 mb-1 opacity-50" />
                <span className="text-xs">Failed to load audio</span>
              </div>
            </div>
          );
        }

        return audioUrl.url ? (
          <div className="p-3 border rounded-md">
            <div className="flex items-center mb-2">
              <FileAudio className="h-5 w-5 text-amber-500 mr-2" />
              <span className="font-medium">{(data as any).title || "Audio file"}</span>
            </div>
            <audio
              src={audioUrl.url}
              controls
              className="w-full"
            />
          </div>
        ) : (
          <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <FileAudio className="h-6 w-6 mb-1 opacity-50" />
              <span className="text-xs">No audio uploaded</span>
            </div>
          </div>
        );

      case "embed":
        return (data as any).code ? (
          <div className="p-3 border rounded-md">
            <div className="mb-2 pb-2 border-b">
              <div className="flex items-center">
                <Code className="h-5 w-5 text-indigo-500 mr-2" />
                <span className="font-medium">Embedded content</span>
              </div>
            </div>
            <div className="border rounded p-2 bg-white">
              <div dangerouslySetInnerHTML={{ __html: (data as any).code }} />
            </div>
          </div>
        ) : (
          <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <Code className="h-6 w-6 mb-1 opacity-50" />
              <span className="text-xs">No embed code added</span>
            </div>
          </div>
        );

      case "scorm":
        return (data as any).fileName ? (
          <div className="p-3 border rounded-md">
            <div className="flex items-center mb-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded mr-3">
                <Boxes className="h-6 w-6 text-orange-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).title || (data as any).fileName}
                </div>
                <div className="text-xs text-muted-foreground">
                  SCORM Package
                  {(data as any).fileSize
                    ? ` (${Math.round((data as any).fileSize / (1024 * 1024))} MB)`
                    : ""}
                </div>
              </div>
            </div>
            <div className="mt-2">
              <Button variant="outline" className="w-full text-sm">
                Launch SCORM Content
              </Button>
            </div>
          </div>
        ) : (
          <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <Boxes className="h-6 w-6 mb-1 opacity-50" />
              <span className="text-xs">No SCORM package uploaded</span>
            </div>
          </div>
        );

      case "webgl":
        return ((data as any).src || (data as any).fileName) ? (
          <div className="p-3 border rounded-md">
            <div className="flex items-center mb-3">
              <div className="p-2 bg-cyan-100 dark:bg-cyan-900 rounded mr-3">
                <Gamepad2 className="h-6 w-6 text-cyan-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).title || (data as any).fileName || "WebGL Content"}
                </div>
                <div className="text-xs text-muted-foreground">
                  WebGL Interactive Content
                </div>
              </div>
            </div>
            <div className="mt-2">
              <Button variant="outline" className="w-full text-sm">
                Launch WebGL Content
              </Button>
            </div>
          </div>
        ) : (
          <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <Gamepad2 className="h-6 w-6 mb-1 opacity-50" />
              <span className="text-xs">No WebGL content uploaded</span>
            </div>
          </div>
        );

      case "attachment":
        if (attachmentUrl.isLoading) {
          return (
            <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <Loader2 className="h-6 w-6 mb-1 opacity-50 animate-spin" />
                <span className="text-xs">Loading attachment...</span>
              </div>
            </div>
          );
        }

        if (attachmentUrl.error) {
          return (
            <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
              <div className="text-muted-foreground flex flex-col items-center">
                <FileArchive className="h-6 w-6 mb-1 opacity-50" />
                <span className="text-xs">Failed to load attachment</span>
              </div>
            </div>
          );
        }

        return (data as any).fileName ? (
          <div className="p-3 border rounded-md">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded mr-3">
                <FileArchive className="h-6 w-6 text-blue-500" />
              </div>
              <div className="flex-1">
                <div className="font-medium">
                  {(data as any).title || (data as any).fileName || "File Attachment"}
                </div>
                <div className="text-xs text-muted-foreground">
                  File Attachment
                  {(data as any).fileSize
                    ? ` • ${Math.round((data as any).fileSize / (1024 * 1024))} MB`
                    : ""}
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (attachmentUrl.url) {
                    window.open(attachmentUrl.url, '_blank');
                  }
                }}
                disabled={!attachmentUrl.url}
              >
                Download
              </Button>
            </div>
          </div>
        ) : (
          <div className="p-3 border rounded-md bg-slate-50 dark:bg-slate-700 text-center">
            <div className="text-muted-foreground flex flex-col items-center">
              <FileArchive className="h-6 w-6 mb-1 opacity-50" />
              <span className="text-xs">No file attached</span>
            </div>
          </div>
        );

      case "mcq":
        return (
          <div>
            <div className="flex items-center mb-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded mr-3">
                <ListChecks className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).question || "Multiple Choice Question"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} multiple choice question
                </div>
              </div>
            </div>
            <div className="space-y-2">
              {(data as any).options?.map((option: any, index: number) => (
                <div key={option.id || index} className="flex items-center">
                  <div className="h-4 w-4 rounded-full border mr-2"></div>
                  <span>{option.text}</span>
                </div>
              )) || (
                <span className="text-muted-foreground italic">No options added</span>
              )}
            </div>
          </div>
        );

      case "textbox":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded mr-3">
                <FormInput className="h-6 w-6 text-green-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Text Input Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} text input field
                </div>
              </div>
            </div>
          </div>
        );

      case "feedback-image":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded mr-3">
                <Upload className="h-6 w-6 text-purple-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Image Upload Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} image upload field
                </div>
              </div>
            </div>
          </div>
        );

      case "option":
        return (
          <div>
            <div className="flex items-center mb-3">
              <div className="p-2 bg-amber-100 dark:bg-amber-900 rounded mr-3">
                <FileQuestion className="h-6 w-6 text-amber-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Option Question"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} option selection
                </div>
              </div>
            </div>
            <div className="mt-2 space-y-2">
              {(data as any).options?.slice(0, 3).map((option: any, index: number) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-full border border-amber-500"></div>
                  <span className="text-sm">{option.text}</span>
                </div>
              )) || (
                <div className="text-muted-foreground italic">No options added</div>
              )}
              {(data as any).options?.length > 3 && (
                <div className="text-xs text-muted-foreground">
                  +{(data as any).options.length - 3} more options
                </div>
              )}
            </div>
          </div>
        );

      case "checkbox":
        return (
          <div>
            <div className="flex items-center mb-3">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded mr-3">
                <CheckSquare className="h-6 w-6 text-indigo-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Checkbox Question"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} checkbox selection
                </div>
              </div>
            </div>
            <div className="mt-2 space-y-2">
              {(data as any).options?.map((option: any, index: number) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="h-4 w-4 rounded-sm border border-indigo-500"></div>
                  <span className="text-sm">{option.text}</span>
                </div>
              )) || (
                ['Yes', 'No', 'Not Applicable'].map((defaultOption, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-sm border border-indigo-500"></div>
                    <span className="text-sm">{defaultOption}</span>
                  </div>
                ))
              )}
            </div>
          </div>
        );

      case "star":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded mr-3">
                <Star className="h-6 w-6 text-yellow-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Rating Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} rating field
                </div>
              </div>
            </div>
          </div>
        );

      case "date":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-red-100 dark:bg-red-900 rounded mr-3">
                <Calendar className="h-6 w-6 text-red-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Date Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} date field
                </div>
              </div>
            </div>
          </div>
        );

      case "time":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-cyan-100 dark:bg-cyan-900 rounded mr-3">
                <Clock className="h-6 w-6 text-cyan-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Time Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} time field
                </div>
              </div>
            </div>
          </div>
        );

      case "duration":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-slate-100 dark:bg-slate-700 rounded mr-3">
                <Timer className="h-6 w-6 text-slate-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Duration Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} duration field
                </div>
              </div>
            </div>
          </div>
        );

      case "phone":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-emerald-100 dark:bg-emerald-900 rounded mr-3">
                <Phone className="h-6 w-6 text-emerald-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Phone Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} phone field
                </div>
              </div>
            </div>
          </div>
        );

      case "alphanumeric":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded mr-3">
                <Hash className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Alphanumeric Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} alphanumeric field
                  {(data as any).containNumber && (data as any).containLetters
                    ? " (Numbers and Letters)"
                    : (data as any).containNumber
                      ? " (Numbers)"
                      : (data as any).containLetters
                        ? " (Letters)"
                        : ""}
                </div>
              </div>
            </div>
          </div>
        );

      case "sign":
        return (
          <div>
            <div className="flex items-center">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded mr-3">
                <PenTool className="h-6 w-6 text-indigo-500" />
              </div>
              <div>
                <div className="font-medium">
                  {(data as any).label || "Signature Field"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {(data as any).required ? "Required" : "Optional"} signature field
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Preview not available for this component type
            </AlertDescription>
          </Alert>
        );
    }
  };

  return (
    <div className={`component-preview ${isPreview ? "p-4 border rounded-md" : ""}`}>
      {renderPreview()}
    </div>
  );
};

export default ComponentPreview;
