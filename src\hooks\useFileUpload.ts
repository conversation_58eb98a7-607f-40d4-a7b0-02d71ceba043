import { useState } from 'react';
import { uploadFile, validateFile, FILE_TYPES } from '@/utils/fileUtils';
import { toast } from '@/hooks/use-toast';

interface UseFileUploadOptions {
  allowedTypes?: string[];
  maxSizeInMB?: number;
  onUploadSuccess?: (fileInfo: any) => void;
  onUploadError?: (error: Error) => void;
}

interface FileUploadState {
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
}

export const useFileUpload = (options: UseFileUploadOptions = {}) => {
  const {
    allowedTypes = [],
    maxSizeInMB = 50,
    onUploadSuccess,
    onUploadError
  } = options;

  const [uploadState, setUploadState] = useState<FileUploadState>({
    isUploading: false,
    uploadProgress: 0,
    error: null
  });

  const handleFileUpload = async (file: File) => {
    // Reset state
    setUploadState({
      isUploading: true,
      uploadProgress: 0,
      error: null
    });

    try {
      // Validate file
      const validation = validateFile(file, allowedTypes, maxSizeInMB);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Simulate progress for better UX
      setUploadState(prev => ({ ...prev, uploadProgress: 25 }));

      // Upload file
      const fileInfo = await uploadFile(file);
      
      setUploadState(prev => ({ ...prev, uploadProgress: 100 }));

      // Success callback
      if (onUploadSuccess) {
        onUploadSuccess(fileInfo);
      }

      toast({
        title: "Success",
        description: "File uploaded successfully.",
      });

      return fileInfo;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      setUploadState(prev => ({
        ...prev,
        error: errorMessage
      }));

      if (onUploadError) {
        onUploadError(error instanceof Error ? error : new Error(errorMessage));
      }

      toast({
        title: "Upload Failed",
        description: errorMessage,
        variant: "destructive",
      });

      throw error;
    } finally {
      setUploadState(prev => ({
        ...prev,
        isUploading: false
      }));
    }
  };

  const resetUploadState = () => {
    setUploadState({
      isUploading: false,
      uploadProgress: 0,
      error: null
    });
  };

  return {
    uploadState,
    handleFileUpload,
    resetUploadState,
    // Convenience methods for common file types
    uploadImage: (file: File) => handleFileUpload(file),
    uploadVideo: (file: File) => handleFileUpload(file),
    uploadAudio: (file: File) => handleFileUpload(file),
    uploadDocument: (file: File) => handleFileUpload(file)
  };
};

// Preset hooks for common file types
export const useImageUpload = (options: Omit<UseFileUploadOptions, 'allowedTypes'> = {}) => {
  return useFileUpload({
    ...options,
    allowedTypes: FILE_TYPES.IMAGES,
    maxSizeInMB: options.maxSizeInMB || 10
  });
};

export const useVideoUpload = (options: Omit<UseFileUploadOptions, 'allowedTypes'> = {}) => {
  return useFileUpload({
    ...options,
    allowedTypes: FILE_TYPES.VIDEOS,
    maxSizeInMB: options.maxSizeInMB || 100
  });
};

export const useAudioUpload = (options: Omit<UseFileUploadOptions, 'allowedTypes'> = {}) => {
  return useFileUpload({
    ...options,
    allowedTypes: FILE_TYPES.AUDIO,
    maxSizeInMB: options.maxSizeInMB || 25
  });
};
