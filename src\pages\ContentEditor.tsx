import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save, Eye } from "lucide-react";
import { ContentMode, DroppedItem, ContentComponent } from "@/types";
import Sidebar from "@/components/Sidebar";
import DroppableArea from "@/components/DroppableArea";
import { toast } from "@/hooks/use-toast";
import apiService from "@/services/apiService";

export default function ContentEditor() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const unitId = searchParams.get('unitId');

  const [activeMode, setActiveMode] = useState<ContentMode>("communicate");
  const [items, setItems] = useState<DroppedItem[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [unitInfo, setUnitInfo] = useState<{ name: string; description?: string } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDevice, setSelectedDevice] = useState<'iphone8' | 'iphoneX' | 'android'>('iphone8');

  // Device configurations for mobile preview
  const deviceConfigs = {
    iphone8: { width: 375, height: 667, name: 'iPhone 8' },
    iphoneX: { width: 375, height: 812, name: 'iPhone X' },
    android: { width: 360, height: 640, name: 'Android' }
  };

  // Load unit information and content when component mounts
  useEffect(() => {
    if (unitId) {
      loadUnitInfo(unitId);
    } else {
      // Redirect back if no unitId provided
      navigate('/curate');
    }
  }, [unitId, navigate]);

  const loadUnitInfo = async (id: string) => {
    setIsLoading(true);
    try {
      console.log('Loading unit information for:', id);

      // Fetch unit information from API
      const unitData = await apiService.getKnowledgeUnit(id);

      setUnitInfo({
        name: unitData.name,
        description: unitData.description
      });

      // Load unit content
      await loadUnitContent(id);

    } catch (error) {
      console.error('Error loading unit information:', error);
      toast({
        title: "Error",
        description: "Failed to load unit information.",
        variant: "destructive",
      });
      navigate('/curate');
    } finally {
      setIsLoading(false);
    }
  };

  const loadUnitContent = async (id: string) => {
    try {
      console.log('Loading content for unit:', id);
      // Get the unit data which may contain items
      const unitData = await apiService.getKnowledgeUnit(id);

      // Check if the unit has value property
      if (unitData && 'value' in unitData && Array.isArray((unitData as any).value)) {
        // Transform API response back to DroppedItem format
        const transformedItems: DroppedItem[] = ((unitData as any).value || []).map((item: any) => ({
          id: item.id || `item-${Date.now()}-${Math.random()}`,
          type: item.type,
          data: item
        }));

        setItems(transformedItems);
        console.log('Loaded content items:', transformedItems.length);
      } else {
        console.log('No content items found for unit');
        setItems([]);
      }
    } catch (error) {
      console.error('Error loading unit content:', error);
      // Don't show error toast for missing content - it's normal for new units
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        if (axiosError.response?.status !== 404) {
          toast({
            title: "Error",
            description: "Failed to load unit content.",
            variant: "destructive",
          });
        }
      }
    }
  };

  const handleModeChange = (mode: ContentMode) => {
    setActiveMode(mode);
  };

  const handleDragStart = (type: string) => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleAddItem = (item: DroppedItem) => {
    setItems((prev) => [...prev, item]);
  };

  const handleRemoveItem = (id: string) => {
    setItems((prev) => prev.filter((item) => item.id !== id));
  };

  const handleUpdateItem = (id: string, data: ContentComponent) => {
    setItems((prev) =>
      prev.map((item) => (item.id === id ? { ...item, data } : item))
    );
  };

  const handleReorderItems = (sourceId: string, targetId: string) => {
    setItems((prev) => {
      const sourceIndex = prev.findIndex((item) => item.id === sourceId);
      const targetIndex = prev.findIndex((item) => item.id === targetId);

      if (sourceIndex === -1 || targetIndex === -1) return prev;

      const newItems = [...prev];
      const [movedItem] = newItems.splice(sourceIndex, 1);
      newItems.splice(targetIndex, 0, movedItem);

      // Update position property for all items
      return newItems.map((item, index) => ({
        ...item,
        data: { ...item.data, position: index }
      }));
    });
  };

  const handleSave = async () => {
    if (!unitId) {
      toast({
        title: "Error",
        description: "No unit ID provided.",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    try {
      // Transform items to the format expected by the API
      const contentItems = items.map(item => ({
        ...item.data,
        id: item.id,
        type: item.type
      }));

      console.log('Saving content for unit:', unitId);
      console.log('Content items:', contentItems);

      await apiService.updateKnowledgeUnitContent(unitId, contentItems);

      toast({
        title: "Success",
        description: "Unit content saved successfully.",
      });
    } catch (error) {
      console.error('Error saving unit content:', error);
      toast({
        title: "Error",
        description: "Failed to save unit content.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Test function for presigned URL
  const testPresignedUrl = async () => {
    try {
      const testFileName = "1752322483212photo_2025-07-10_20-43-58.jpg";
      console.log('Testing presigned URL for:', testFileName);
      const url = await apiService.getFileDownloadUrl(testFileName);
      console.log('Got presigned URL:', url);
      toast({
        title: "Test Success",
        description: `Got presigned URL: ${url.substring(0, 50)}...`,
      });
    } catch (error) {
      console.error('Test failed:', error);
      toast({
        title: "Test Failed",
        description: "Failed to get presigned URL",
        variant: "destructive",
      });
    }
  };

  const handleBack = () => {
    navigate('/curate');
  };

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  return (
    <div className="fixed inset-0 w-full h-full bg-slate-50 dark:bg-slate-900 z-50">
      {/* Minimal header */}
      <div className="absolute top-0 left-0 right-0 bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 px-4 py-2 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="flex items-center space-x-2"
            >
              <ArrowLeft size={16} />
              <span>Back</span>
            </Button>
            <div className="h-4 w-px bg-slate-300 dark:bg-slate-600" />
            <div>
              <h1 className="text-lg font-medium text-slate-900 dark:text-slate-100">
                {isLoading ? 'Loading...' : (unitInfo?.name || 'Untitled Unit')}
              </h1>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {isPreviewMode && (
              <select
                value={selectedDevice}
                onChange={(e) => setSelectedDevice(e.target.value as 'iphone8' | 'iphoneX' | 'android')}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              >
                <option value="iphone8">iPhone 8</option>
                <option value="iphoneX">iPhone X</option>
                <option value="android">Android</option>
              </select>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={testPresignedUrl}
              className="flex items-center space-x-2"
            >
              <span>Test URL</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={togglePreview}
              className="flex items-center space-x-2"
            >
              <Eye size={16} />
              <span>{isPreviewMode ? 'Edit' : 'Mobile Preview'}</span>
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
              size="sm"
              className="flex items-center space-x-2"
            >
              <Save size={16} />
              <span>{isSaving ? 'Saving...' : 'Save'}</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Full-screen content area */}
      <div className="absolute inset-0 pt-14 flex overflow-hidden" onDragEnd={handleDragEnd}>
        {/* Content Components Sidebar */}
        {!isPreviewMode && (
          <div className="w-80 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col">
            <Sidebar
              activeMode={activeMode}
              onDragStart={handleDragStart}
              onChange={handleModeChange}
            />
          </div>
        )}

        {/* Main editing/preview area */}
        <div className="flex-1 overflow-auto">
          {isPreviewMode ? (
            /* Mobile Preview Mode */
            <div className="h-full bg-gray-100 dark:bg-gray-900 flex items-center justify-center p-8">
              <div className="relative">
                {/* Mobile Frame */}
                <div className="bg-black rounded-[2.5rem] p-2 shadow-2xl">
                  <div
                    className="bg-white dark:bg-gray-800 rounded-[2rem] overflow-hidden"
                    style={{
                      width: `${deviceConfigs[selectedDevice].width}px`,
                      height: `${deviceConfigs[selectedDevice].height}px`
                    }}
                  >
                    {/* Mobile Status Bar */}
                    <div className="bg-gray-900 text-white text-xs px-4 py-1 flex justify-between items-center">
                      <span>9:41</span>
                      <div className="flex items-center space-x-1">
                        <div className="w-4 h-2 border border-white rounded-sm">
                          <div className="w-3 h-1 bg-white rounded-sm m-0.5"></div>
                        </div>
                      </div>
                    </div>

                    {/* Mobile App Header */}
                    <div className="bg-blue-600 text-white px-4 py-3 flex items-center">
                      <div className="w-6 h-6 bg-white bg-opacity-20 rounded mr-3"></div>
                      <h1 className="font-medium text-sm truncate">{unitInfo?.name || 'Unit'}</h1>
                    </div>

                    {/* Mobile Content Area */}
                    <div
                      className="overflow-auto bg-white dark:bg-gray-800"
                      style={{ height: `${deviceConfigs[selectedDevice].height - 60}px` }}
                    >
                      <DroppableArea
                        activeMode={activeMode}
                        items={items}
                        onAddItem={handleAddItem}
                        onRemoveItem={handleRemoveItem}
                        onUpdateItem={handleUpdateItem}
                        onReorderItems={handleReorderItems}
                        isPreviewMode={isPreviewMode}
                      />
                    </div>
                  </div>
                </div>

                {/* Mobile Frame Label */}
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gray-800 text-white text-xs px-3 py-1 rounded-full">
                    {deviceConfigs[selectedDevice].name} ({deviceConfigs[selectedDevice].width}×{deviceConfigs[selectedDevice].height})
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Normal Editing Mode */
            <DroppableArea
              activeMode={activeMode}
              items={items}
              onAddItem={handleAddItem}
              onRemoveItem={handleRemoveItem}
              onUpdateItem={handleUpdateItem}
              onReorderItems={handleReorderItems}
              isPreviewMode={isPreviewMode}
            />
          )}
        </div>
      </div>
    </div>
  );
}
