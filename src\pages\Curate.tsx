import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Search,
  Plus,
  Edit2,
  ChevronRight,
  Grid,
  List,
  Copy,
  Trash,
  FileText,
  Wand2
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "@/hooks/use-toast";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import KnowledgeTree from "@/components/curate/KnowledgeTree";
import { useKnowledge, KnowledgeArea, KnowledgeTopic, KnowledgeUnit, ChecklistFormArea, ChecklistFormTopic, ChecklistFormUnit, KnowledgeItem, ChecklistFormItem } from "@/contexts/KnowledgeContext";
import apiService from "@/services/apiService";

interface KnowledgeArea {
  id: string;
  name: string;
  type: "area";
  icon?: string;
  color?: string;
  children: KnowledgeTopic[];
  topicCount?: number;
}

interface KnowledgeTopic {
  id: string;
  name: string;
  type: "topic";
  unitCount?: number;
  children: KnowledgeUnit[];
}

interface KnowledgeUnit {
  id: string;
  name: string;
  type: "unit";
  stepCount?: number;
  createdOn?: string;
  updatedOn?: string;
  isDefault?: boolean;
}

interface ChecklistFormArea {
  id: string;
  name: string;
  type: "area";
  topicCount: number;
  children: ChecklistFormTopic[];
}

interface ChecklistFormTopic {
  id: string;
  name: string;
  type: "topic";
  unitCount: number;
  children: ChecklistFormUnit[];
}

interface ChecklistFormUnit {
  id: string;
  name: string;
  type: "unit";
  category: "checklist" | "form";
  itemCount: number;
  createdOn: string;
  updatedOn: string;
  isDefault?: boolean;
}

type KnowledgeItem = KnowledgeArea | KnowledgeTopic | KnowledgeUnit;
type ChecklistFormItem = ChecklistFormArea | ChecklistFormTopic | ChecklistFormUnit;

// Initial data for knowledge areas, topics, and units
const initialData: KnowledgeArea[] = [];

// Initial data for checklists and forms
const initialChecklistFormData: ChecklistFormArea[] = [];

export default function Curate() {
  // Use the shared knowledge context
  const { knowledgeData, checklistFormData, setKnowledgeData, setChecklistFormData } = useKnowledge();
  const navigate = useNavigate();
  const [formOpen, setFormOpen] = useState(false);
  const [formType, setFormType] = useState<"area" | "topic" | "unit">("area");
  const [isEditing, setIsEditing] = useState(false);
  const [currentParentId, setCurrentParentId] = useState<string | null>(null);
  const [currentItem, setCurrentItem] = useState<ChecklistFormItem | null>(null);
  const [selectedArea, setSelectedArea] = useState<ChecklistFormArea | null>(checklistFormData[0] || null);
  const [selectedTopic, setSelectedTopic] = useState<ChecklistFormTopic | null>(
    selectedArea?.children[0] || null
  );
  const [searchArea, setSearchArea] = useState("");
  const [searchTopic, setSearchTopic] = useState("");
  const [searchUnit, setSearchUnit] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [activeTab, setActiveTab] = useState<"knowledge" | "checklistform">("knowledge");

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load knowledge areas on component mount
  useEffect(() => {
    const loadKnowledgeAreas = async () => {
      if (activeTab === "knowledge" && knowledgeData.length === 0) {
        setIsLoading(true);
        try {
          const areas = await apiService.getKnowledgeAreas();

          // Transform API response to match the component's data structure
          const transformedAreas: KnowledgeArea[] = areas.map(area => ({
            id: area.id,
            name: area.name,
            type: "area" as const,
            topicCount: area.topicCount || 0,
            description: area.description,
            children: [] // Topics will be loaded when area is selected
          }));

          setKnowledgeData(transformedAreas);

          // Auto-select first area if available
          if (transformedAreas.length > 0 && !selectedArea) {
            setSelectedArea(transformedAreas[0] as any);
          }
        } catch (error) {
          console.error('Error loading knowledge areas:', error);
          toast({
            title: "Error",
            description: "Failed to load knowledge areas. Please refresh the page.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadKnowledgeAreas();
  }, [activeTab, knowledgeData.length, selectedArea, setKnowledgeData, toast]);

  useEffect(() => {
    if (selectedArea) {
      setSelectedTopic(selectedArea.children.length > 0 ? selectedArea.children[0] : null);
    } else {
      setSelectedTopic(null);
    }
  }, [selectedArea]);

  const filteredAreas = knowledgeData.filter(area =>
    area.name.toLowerCase().includes(searchArea.toLowerCase())
  );

  const filteredTopics = selectedArea?.children.filter(topic =>
    topic.name.toLowerCase().includes(searchTopic.toLowerCase())
  ) || [];

  const filteredUnits = selectedTopic?.children.filter(unit =>
    unit.name.toLowerCase().includes(searchUnit.toLowerCase())
  ) || [];



  const handleAddItem = (type: "area" | "topic" | "unit") => {
    setFormType(type);
    setIsEditing(false);
    setName("");
    setDescription("");

    if (type === "topic" && selectedArea) {
      setCurrentParentId(selectedArea.id);
    } else if (type === "unit" && selectedTopic) {
      setCurrentParentId(selectedTopic.id);
    }

    setFormOpen(true);
  };

  const handleEditItem = (item: KnowledgeItem) => {
    setFormType(item.type);
    setIsEditing(true);
    setCurrentItem(item);
    setName(item.name);
    // Set description from the item if it exists, otherwise empty string
    setDescription((item as any).description || "");
    setFormOpen(true);
  };

  const handleDeleteItem = (item: KnowledgeItem) => {
    if (item.type === "area") {
      setKnowledgeData(knowledgeData.filter(area => area.id !== item.id));
      if (selectedArea?.id === item.id) {
        setSelectedArea(null);
        setSelectedTopic(null);
      }
    } else if (item.type === "topic" && selectedArea) {
      const updatedAreas = knowledgeData.map(area => {
        if (area.id === selectedArea.id) {
          return {
            ...area,
            children: area.children.filter(topic => topic.id !== item.id),
            topicCount: (area.topicCount || 0) - 1
          };
        }
        return area;
      });

      setKnowledgeData(updatedAreas);
      if (selectedTopic?.id === item.id) {
        setSelectedTopic(null);
      }

      const updatedArea = updatedAreas.find(a => a.id === selectedArea.id);
      if (updatedArea) {
        setSelectedArea(updatedArea);
      }
    } else if (item.type === "unit" && selectedTopic && selectedArea) {
      const updatedAreas = knowledgeData.map(area => {
        if (area.id === selectedArea.id) {
          return {
            ...area,
            children: area.children.map(topic => {
              if (topic.id === selectedTopic.id) {
                return {
                  ...topic,
                  children: topic.children.filter(unit => unit.id !== item.id),
                  unitCount: (topic.unitCount || 0) - 1
                };
              }
              return topic;
            })
          };
        }
        return area;
      });

      setKnowledgeData(updatedAreas);

      const updatedArea = updatedAreas.find(a => a.id === selectedArea.id);
      if (updatedArea) {
        setSelectedArea(updatedArea);
        const updatedTopic = updatedArea.children.find(t => t.id === selectedTopic.id);
        if (updatedTopic) {
          setSelectedTopic(updatedTopic);
        }
      }
    }

    toast({
      title: "Item deleted",
      description: `${item.name} has been deleted.`,
    });
  };

  const handleCurateClick = (unitId: string) => {
    // Navigate to the separate ContentEditor page with only unitId
    navigate(`/content-editor?unitId=${unitId}`);
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (!isEditing) {
        if (activeTab === "knowledge") {
          // Handle Knowledge tab API calls
          if (formType === "area") {
            const createdArea = await apiService.createKnowledgeArea({
              name,
              description: description || undefined
            });

            const newKnowledgeArea: KnowledgeArea = {
              id: createdArea.id,
              name: createdArea.name,
              type: "area",
              topicCount: 0,
              children: []
            };

            setKnowledgeData([...knowledgeData, newKnowledgeArea]);
          } else if (formType === "topic" && selectedArea) {
            const createdTopic = await apiService.createKnowledgeTopic(selectedArea.id, {
              name,
              description: description || undefined
            });

            const newKnowledgeTopic: KnowledgeTopic = {
              id: createdTopic.id,
              name: createdTopic.name,
              type: "topic",
              unitCount: 0,
              children: []
            };

            const updatedAreas = knowledgeData.map(area => {
              if (area.id === selectedArea.id) {
                return {
                  ...area,
                  children: [...area.children, newKnowledgeTopic],
                  topicCount: (area.topicCount || 0) + 1
                };
              }
              return area;
            });

            setKnowledgeData(updatedAreas);
          } else if (formType === "unit" && selectedTopic) {
            const createdUnit = await apiService.createKnowledgeUnit(selectedTopic.id, {
              name,
              description: description || undefined
            });

            const today = new Date();
            const dateString = today.toISOString().split('T')[0].replace(/-/g, '-');
            const timeString = today.toTimeString().split(' ')[0];
            const dateTimeString = `${dateString} ${timeString}`;

            const newKnowledgeUnit: KnowledgeUnit = {
              id: createdUnit.id,
              name: createdUnit.name,
              type: "unit",
              stepCount: 0,
              createdOn: dateTimeString,
              updatedOn: dateTimeString,
              isDefault: false
            };

            const updatedAreas = knowledgeData.map(area => {
              if (area.id === selectedArea?.id) {
                return {
                  ...area,
                  children: area.children.map(topic => {
                    if (topic.id === selectedTopic.id) {
                      return {
                        ...topic,
                        children: [...topic.children, newKnowledgeUnit],
                        unitCount: (topic.unitCount || 0) + 1
                      };
                    }
                    return topic;
                  })
                };
              }
              return area;
            });

            setKnowledgeData(updatedAreas);
          }
        } else {
          // Handle Checklist/Form tab (existing logic)
          const newId = `id-${Date.now()}`;

          if (formType === "area") {
            const newArea: ChecklistFormArea = {
              id: newId,
              name,
              type: "area",
              topicCount: 0,
              children: []
            };

            setChecklistFormData([...checklistFormData, newArea]);
          } else if (formType === "topic" && selectedArea) {
        const newTopic: ChecklistFormTopic = {
          id: newId,
          name,
          type: "topic",
          unitCount: 0,
          children: []
        };

        const updatedAreas = checklistFormData.map(area => {
          if (area.id === selectedArea.id) {
            return {
              ...area,
              children: [...area.children, newTopic],
              topicCount: (area.topicCount || 0) + 1
            };
          }
          return area;
        });

        setChecklistFormData(updatedAreas);
      } else if (formType === "unit" && selectedTopic && selectedArea) {
        const today = new Date();
        const dateString = today.toISOString().split('T')[0].replace(/-/g, '-');
        const timeString = today.toTimeString().split(' ')[0];
        const dateTimeString = `${dateString} ${timeString}`;

        const category = document.getElementById("category") as HTMLSelectElement;
        const categoryValue = category?.value as "checklist" | "form" || "checklist";
        const isDefault = (document.getElementById("isDefault") as HTMLInputElement)?.checked || false;

        const newUnit: ChecklistFormUnit = {
          id: newId,
          name,
          type: "unit",
          category: categoryValue,
          itemCount: 0,
          createdOn: dateTimeString,
          updatedOn: dateTimeString,
          isDefault
        };

        const updatedAreas = checklistFormData.map(area => {
          if (area.id === selectedArea.id) {
            return {
              ...area,
              children: area.children.map(topic => {
                if (topic.id === selectedTopic.id) {
                  return {
                    ...topic,
                    children: [...topic.children, newUnit],
                    unitCount: (topic.unitCount || 0) + 1
                  };
                }
                return topic;
              })
            };
          }
          return area;
        });

        setChecklistFormData(updatedAreas);
      }

        }

        toast({
          title: "Item created",
          description: `${name} has been created.`,
        });
      } else if (currentItem) {
        // Handle editing (existing logic for both tabs)
        if (activeTab === "knowledge") {
          // Handle Knowledge tab editing
          if (currentItem.type === "area") {
            const updatedAreas = knowledgeData.map(area => {
              if (area.id === currentItem.id) {
                return { ...area, name, description };
              }
              return area;
            });

            setKnowledgeData(updatedAreas);

            // Update selected area if it's the one being edited
            if (selectedArea?.id === currentItem.id) {
              const updatedSelectedArea = updatedAreas.find(a => a.id === currentItem.id);
              if (updatedSelectedArea) {
                setSelectedArea(updatedSelectedArea as any);
              }
            }
          } else if (currentItem.type === "topic" && selectedArea) {
            const updatedAreas = knowledgeData.map(area => {
              if (area.id === selectedArea.id) {
                return {
                  ...area,
                  children: area.children.map(topic => {
                    if (topic.id === currentItem.id) {
                      return { ...topic, name, description };
                    }
                    return topic;
                  })
                };
              }
              return area;
            });

            setKnowledgeData(updatedAreas);

            // Update selected topic if it's the one being edited
            if (selectedTopic?.id === currentItem.id) {
              const updatedSelectedArea = updatedAreas.find(a => a.id === selectedArea.id);
              if (updatedSelectedArea) {
                const updatedSelectedTopic = updatedSelectedArea.children.find(t => t.id === currentItem.id);
                if (updatedSelectedTopic) {
                  setSelectedTopic(updatedSelectedTopic as any);
                }
              }
            }
          } else if (currentItem.type === "unit" && selectedTopic && selectedArea) {
            const updatedAreas = knowledgeData.map(area => {
              if (area.id === selectedArea.id) {
                return {
                  ...area,
                  children: area.children.map(topic => {
                    if (topic.id === selectedTopic.id) {
                      return {
                        ...topic,
                        children: topic.children.map(unit => {
                          if (unit.id === currentItem.id) {
                            return { ...unit, name, description };
                          }
                          return unit;
                        })
                      };
                    }
                    return topic;
                  })
                };
              }
              return area;
            });

            setKnowledgeData(updatedAreas);
          }
        } else if (activeTab === "checklistform") {
          // Handle Checklist/Form tab editing
          if (currentItem.type === "area") {
            const updatedAreas = checklistFormData.map(area => {
              if (area.id === currentItem.id) {
                return { ...area, name, description };
              }
              return area;
            });

            setChecklistFormData(updatedAreas);
          } else if (currentItem.type === "topic" && selectedArea) {
            const updatedAreas = checklistFormData.map(area => {
              if (area.id === selectedArea.id) {
                return {
                  ...area,
                  children: area.children.map(topic => {
                    if (topic.id === currentItem.id) {
                      return { ...topic, name, description };
                    }
                    return topic;
                  })
                };
              }
              return area;
            });

            setChecklistFormData(updatedAreas);
          } else if (currentItem.type === "unit" && selectedTopic && selectedArea) {
            const updatedAreas = checklistFormData.map(area => {
              if (area.id === selectedArea.id) {
                return {
                  ...area,
                  children: area.children.map(topic => {
                    if (topic.id === selectedTopic.id) {
                      return {
                        ...topic,
                        children: topic.children.map(unit => {
                          if (unit.id === currentItem.id) {
                            const category = document.getElementById("category") as HTMLSelectElement;
                            const categoryValue = category?.value as "checklist" | "form" || unit.category;
                            const isDefault = (document.getElementById("isDefault") as HTMLInputElement)?.checked || false;
                            return { ...unit, name, description, category: categoryValue, isDefault };
                      }
                      return unit;
                    })
                  };
                }
                return topic;
              })
            };
          }
          return area;
        });

        setChecklistFormData(updatedAreas);
      }

        toast({
          title: "Item updated",
          description: `${name} has been updated.`,
        });
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast({
        title: "Error",
        description: "Failed to save item. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setFormOpen(false);
    }
  };

  const selectArea = async (area: KnowledgeArea) => {
    console.log('selectArea called with:', area);
    console.log('Area children length:', area.children.length);
    console.log('Current activeTab:', activeTab);

    setSelectedTopic(null);

    // Load topics for the selected area if not already loaded
    if (activeTab === "knowledge" && area.children.length === 0) {
      console.log('Loading topics for area:', area.id);
      try {
        const topics = await apiService.getKnowledgeTopics(area.id);
        console.log('Fetched topics:', topics);

        // Transform API response to match the component's data structure
        const transformedTopics: KnowledgeTopic[] = topics.map(topic => ({
          id: topic.id,
          name: topic.name,
          type: "topic" as const,
          unitCount: topic.unitCount || 0,
          description: topic.description,
          children: [] // Units will be loaded when topic is selected
        }));

        console.log('Transformed topics:', transformedTopics);

        // Update the area with its topics
        const updatedAreas = knowledgeData.map(a => {
          if (a.id === area.id) {
            return {
              ...a,
              children: transformedTopics
            };
          }
          return a;
        });

        console.log('Updated areas with topics:', updatedAreas);
        setKnowledgeData(updatedAreas);

        // Update the selected area to reflect the new children
        const updatedSelectedArea = updatedAreas.find(a => a.id === area.id);
        if (updatedSelectedArea) {
          console.log('Setting updated selected area:', updatedSelectedArea);
          setSelectedArea(updatedSelectedArea as any);

          // Auto-select first topic if available
          if (transformedTopics.length > 0) {
            console.log('Auto-selecting first topic:', transformedTopics[0]);
            setSelectedTopic(transformedTopics[0] as any);
          }
        }
      } catch (error) {
        console.error('Error loading knowledge topics:', error);
        toast({
          title: "Error",
          description: "Failed to load topics for this area.",
          variant: "destructive",
        });
      }
    } else {
      console.log('Area already has children or not knowledge tab');
      setSelectedArea(area as any);
      if (area.children.length > 0) {
        setSelectedTopic(area.children[0] as any);
      }
    }
  };

  const selectTopic = async (topic: KnowledgeTopic) => {
    console.log('selectTopic called with:', topic);
    console.log('Current activeTab:', activeTab);
    console.log('Topic children length:', topic.children.length);

    setSelectedTopic(topic as any);

    // Load units for the selected topic if not already loaded
    if (activeTab === "knowledge" && topic.children.length === 0) {
      console.log('Loading units for topic:', topic.id);
      try {
        const units = await apiService.getKnowledgeUnits(topic.id);
        console.log('Fetched units:', units);

        // Transform API response to match the component's data structure
        const transformedUnits: KnowledgeUnit[] = units.map(unit => ({
          id: unit.id,
          name: unit.name,
          type: "unit" as const,
          stepCount: unit.stepCount || 0,
          description: unit.description,
          createdOn: unit.createdAt || new Date().toISOString().split('T')[0],
          updatedOn: unit.updatedAt || new Date().toISOString().split('T')[0],
          isDefault: unit.isDefault || false
        }));

        console.log('Transformed units:', transformedUnits);

        // Update the topic with its units
        const updatedAreas = knowledgeData.map(area => {
          if (area.id === selectedArea?.id) {
            return {
              ...area,
              children: area.children.map(t => {
                if (t.id === topic.id) {
                  return {
                    ...t,
                    children: transformedUnits
                  };
                }
                return t;
              })
            };
          }
          return area;
        });

        console.log('Updated areas with units:', updatedAreas);
        setKnowledgeData(updatedAreas);

        // Update the selected topic to reflect the new children
        const updatedSelectedArea = updatedAreas.find(a => a.id === selectedArea?.id);
        if (updatedSelectedArea) {
          const updatedSelectedTopic = updatedSelectedArea.children.find(t => t.id === topic.id);
          if (updatedSelectedTopic) {
            setSelectedTopic(updatedSelectedTopic as any);
          }
        }
      } catch (error) {
        console.error('Error loading knowledge units:', error);
        toast({
          title: "Error",
          description: "Failed to load units for this topic.",
          variant: "destructive",
        });
      }
    } else {
      console.log('Not loading units - either not knowledge tab or topic already has children');
    }
  };

  const getCircleColorClass = (index: number) => {
    const colors = [
      "bg-purple-600",
      "bg-gray-600",
      "bg-red-600",
      "bg-purple-600"
    ];

    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Content Curation</h1>
        <div className="flex space-x-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="icon"
            onClick={() => setViewMode("grid")}
          >
            <Grid size={18} />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="icon"
            onClick={() => setViewMode("list")}
          >
            <List size={18} />
          </Button>
        </div>
      </div>

      <div className="flex border-b mb-4">
        <Button
          variant={activeTab === "knowledge" ? "default" : "ghost"}
          className={`rounded-none ${activeTab === "knowledge" ? "border-b-2 border-purple-600" : ""}`}
          onClick={() => setActiveTab("knowledge")}
        >
          Knowledge Curation
        </Button>
        <Button
          variant={activeTab === "checklistform" ? "default" : "ghost"}
          className={`rounded-none ${activeTab === "checklistform" ? "border-b-2 border-purple-600" : ""}`}
          onClick={() => setActiveTab("checklistform")}
        >
          Checklist and Form Curation
        </Button>
      </div>

      {activeTab === "knowledge" && (
        viewMode === "list" ? (
          <KnowledgeTree
            data={knowledgeData}
            onAdd={handleAddItem}
            onEdit={handleEditItem}
            onDelete={handleDeleteItem}
            onCurate={handleCurateClick}
            curateItemId={null}
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border shadow-sm h-[calc(100vh-200px)] flex flex-col overflow-hidden">
              <div className="bg-purple-800 text-white px-4 py-3 font-medium flex justify-between items-center">
                <div>Area</div>
                <div className="flex items-center space-x-1">
                  <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700">
                    <Plus size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700">
                    <List size={16} />
                  </Button>
                </div>
              </div>

            <div className="p-2 border-b relative">
              <Input
                placeholder="Search areas..."
                value={searchArea}
                onChange={(e) => setSearchArea(e.target.value)}
                className="pl-8"
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>

            <div className="overflow-auto flex-1">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500">
                  Loading areas...
                </div>
              ) : filteredAreas.length > 0 ? (
                filteredAreas.map((area) => (
                <div
                  key={area.id}
                  className={cn(
                    "flex items-center p-2 cursor-pointer border-b hover:bg-gray-50",
                    selectedArea?.id === area.id && "bg-gray-100"
                  )}
                  onClick={() => selectArea(area)}
                >
                  <div className="flex-shrink-0 mr-3">
                    <div className={`h-10 w-10 rounded-full bg-purple-600 flex items-center justify-center text-white font-semibold`}>
                      {area.name.charAt(0).toUpperCase()}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{area.name}</div>
                    <div className="text-xs text-gray-500">{area.topicCount} Topic(s)</div>
                  </div>
                  <div className="ml-2 flex items-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-gray-500 hover:text-purple-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditItem(area);
                      }}
                    >
                      <Edit2 size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-gray-500 hover:text-red-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteItem(area);
                      }}
                    >
                      <Trash size={16} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-gray-500 hover:text-purple-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        selectArea(area);
                      }}
                    >
                      <ChevronRight size={16} />
                    </Button>
                  </div>
                </div>
                ))
              ) : (
                <div className="p-4 text-center text-gray-500">
                  No areas found. Create a new area.
                </div>
              )}
            </div>

            <div className="p-2 mt-auto border-t">
              <Button
                variant="outline"
                onClick={() => handleAddItem("area")}
                className="w-full"
              >
                <Plus size={16} className="mr-1" /> Add Area
              </Button>
            </div>
          </Card>

          <Card className="border shadow-sm h-[calc(100vh-200px)] flex flex-col overflow-hidden">
            <div className="bg-purple-800 text-white px-4 py-3 font-medium flex justify-between items-center">
              <div>Topic</div>
              <div className="flex items-center space-x-1">
                <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700">
                  <Plus size={16} />
                </Button>
                <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700">
                  <List size={16} />
                </Button>
              </div>
            </div>

            <div className="p-2 border-b relative">
              <Input
                placeholder="Search topics..."
                value={searchTopic}
                onChange={(e) => setSearchTopic(e.target.value)}
                className="pl-8"
                disabled={!selectedArea}
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>

            <div className="overflow-auto flex-1">
              {selectedArea ? (
                filteredTopics.length > 0 ? (
                  filteredTopics.map((topic, index) => (
                    <div
                      key={topic.id}
                      className={cn(
                        "flex items-center p-2 cursor-pointer border-b hover:bg-gray-50",
                        selectedTopic?.id === topic.id && "bg-gray-100"
                      )}
                      onClick={() => selectTopic(topic)}
                    >
                      <div className="flex-shrink-0 mr-3">
                        <div className={`h-10 w-10 rounded-full ${getCircleColorClass(index)} flex items-center justify-center text-white font-semibold`}>
                          {index + 1}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{topic.name}</div>
                        <div className="text-xs text-gray-500">{topic.unitCount} Unit(s)</div>
                      </div>
                      <div className="ml-2 flex items-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-purple-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditItem(topic);
                          }}
                        >
                          <Edit2 size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteItem(topic);
                          }}
                        >
                          <Trash size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-purple-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            selectTopic(topic);
                          }}
                        >
                          <ChevronRight size={16} />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    No topics found. Create a new topic in this area.
                  </div>
                )
              ) : (
                <div className="p-4 text-center text-gray-500">
                  Select an area to view topics.
                </div>
              )}
            </div>

            <div className="p-2 mt-auto border-t">
              <Button
                variant="outline"
                onClick={() => handleAddItem("topic")}
                className="w-full"
                disabled={!selectedArea}
              >
                <Plus size={16} className="mr-1" /> Add Topic
              </Button>
            </div>
          </Card>

          <Card className="border shadow-sm h-[calc(100vh-200px)] flex flex-col overflow-hidden">
            <div className="bg-purple-800 text-white px-4 py-3 font-medium flex justify-between items-center">
              <div>Unit</div>
              <div className="flex items-center space-x-1">
                <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700">
                  <Plus size={16} />
                </Button>
                <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700">
                  <List size={16} />
                </Button>
              </div>
            </div>

            <div className="p-2 border-b relative">
              <Input
                placeholder="Search units..."
                value={searchUnit}
                onChange={(e) => setSearchUnit(e.target.value)}
                className="pl-8"
                disabled={!selectedTopic}
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>

            <div className="overflow-auto flex-1">
              {selectedTopic ? (
                filteredUnits.length > 0 ? (
                  filteredUnits.map((unit, index) => (
                    <div key={unit.id}>
                      <div className="flex items-center p-3 border-b hover:bg-gray-50">
                        <div className="flex-shrink-0 mr-3">
                          <div className={`h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center text-white font-semibold text-sm`}>
                            {index + 1}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium flex items-center">
                            {unit.name}
                            {unit.isDefault && <Badge variant="outline" className="ml-2 text-xs">Default</Badge>}
                          </div>
                          <div className="text-xs text-gray-500">
                            {unit.stepCount} Step(s) • Created on: {unit.createdOn}
                            <br />
                            Updated on: {unit.updatedOn}
                          </div>
                        </div>
                        <div className="ml-2 flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-500 hover:text-purple-700"
                            onClick={() => handleEditItem(unit)}
                          >
                            <Edit2 size={16} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-500 hover:text-purple-700"
                          >
                            <Copy size={16} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-purple-600 hover:text-purple-800"
                            onClick={() => handleCurateClick(unit.id)}
                          >
                            <Wand2 size={16} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-500 hover:text-red-600"
                            onClick={() => handleDeleteItem(unit)}
                          >
                            <Trash size={16} />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    No units found. Create a new unit for this topic.
                  </div>
                )
              ) : (
                <div className="p-4 text-center text-gray-500">
                  Select a topic to view units.
                </div>
              )}
            </div>

            <div className="p-2 mt-auto border-t">
              <Button
                variant="outline"
                onClick={() => handleAddItem("unit")}
                className="w-full"
                disabled={!selectedTopic}
              >
                <Plus size={16} className="mr-1" /> Add Unit
              </Button>
            </div>
          </Card>
        </div>
      ))}

      {activeTab === "checklistform" && (
        viewMode === "list" ? (
          <KnowledgeTree
            data={checklistFormData}
            onAdd={handleAddItem}
            onEdit={handleEditItem}
            onDelete={handleDeleteItem}
            onCurate={handleCurateClick}
            curateItemId={null}
            mode="checklistform"
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Area Column */}
            <Card className="border shadow-sm h-[calc(100vh-200px)] flex flex-col overflow-hidden">
            <div className="bg-purple-800 text-white px-4 py-3 font-medium flex justify-between items-center">
                <div>Area</div>
              <div className="flex items-center space-x-1">
                  <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700" onClick={() => handleAddItem("area")}>
                  <Plus size={16} />
                </Button>
                </div>
              </div>

              <div className="p-2 border-b relative">
                <Input
                  placeholder="Search areas..."
                  value={searchArea}
                  onChange={(e) => setSearchArea(e.target.value)}
                  className="pl-8"
                />
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>

              <div className="overflow-auto flex-1">
                {filteredAreas.length > 0 ? (
                  filteredAreas.map((area, index) => (
                    <div
                      key={area.id}
                      className={cn(
                        "flex items-center p-2 cursor-pointer border-b hover:bg-gray-50",
                        selectedArea?.id === area.id && "bg-gray-100"
                      )}
                      onClick={() => selectArea(area)}
                    >
                      <div className="flex-shrink-0 mr-3">
                        <div className={`h-10 w-10 rounded-full ${getCircleColorClass(index)} flex items-center justify-center text-white font-semibold`}>
                          {index + 1}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{area.name}</div>
                        <div className="text-xs text-gray-500">{area.topicCount} Topic(s)</div>
                      </div>
                      <div className="ml-2 flex items-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-purple-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditItem(area);
                          }}
                        >
                          <Edit2 size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteItem(area);
                          }}
                        >
                          <Trash size={16} />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    No areas found. Create a new area.
                  </div>
                )}
              </div>

              <div className="p-2 mt-auto border-t">
                <Button
                  variant="outline"
                  onClick={() => handleAddItem("area")}
                  className="w-full"
                >
                  <Plus size={16} className="mr-1" /> Add Area
                </Button>
              </div>
            </Card>

            {/* Topic Column */}
            <Card className="border shadow-sm h-[calc(100vh-200px)] flex flex-col overflow-hidden">
              <div className="bg-purple-800 text-white px-4 py-3 font-medium flex justify-between items-center">
                <div>Topic</div>
                <div className="flex items-center space-x-1">
                  <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700" onClick={() => handleAddItem("topic")}>
                    <Plus size={16} />
                </Button>
              </div>
            </div>

            <div className="p-2 border-b relative">
              <Input
                  placeholder="Search topics..."
                  value={searchTopic}
                  onChange={(e) => setSearchTopic(e.target.value)}
                className="pl-8"
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>

            <div className="overflow-auto flex-1">
                {selectedArea ? (
                  filteredTopics.length > 0 ? (
                    filteredTopics.map((topic, index) => (
                      <div
                        key={topic.id}
                        className={cn(
                          "flex items-center p-2 cursor-pointer border-b hover:bg-gray-50",
                          selectedTopic?.id === topic.id && "bg-gray-100"
                        )}
                        onClick={() => selectTopic(topic)}
                      >
                        <div className="flex-shrink-0 mr-3">
                          <div className={`h-10 w-10 rounded-full ${getCircleColorClass(index)} flex items-center justify-center text-white font-semibold`}>
                            {index + 1}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium">{topic.name}</div>
                          <div className="text-xs text-gray-500">{topic.unitCount} Unit(s)</div>
                        </div>
                        <div className="ml-2 flex items-center">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-500 hover:text-purple-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditItem(topic);
                            }}
                          >
                            <Edit2 size={16} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-gray-500 hover:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteItem(topic);
                            }}
                          >
                            <Trash size={16} />
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      No topics found. Create a new topic in this area.
                    </div>
                  )
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    Select an area to view topics.
                  </div>
                )}
              </div>

              <div className="p-2 mt-auto border-t">
                <Button
                  variant="outline"
                  onClick={() => handleAddItem("topic")}
                  className="w-full"
                  disabled={!selectedArea}
                >
                  <Plus size={16} className="mr-1" /> Add Topic
                </Button>
              </div>
            </Card>

            {/* Unit Column */}
            <Card className="border shadow-sm h-[calc(100vh-200px)] flex flex-col overflow-hidden">
              <div className="bg-purple-800 text-white px-4 py-3 font-medium flex justify-between items-center">
                <div>Unit</div>
                <div className="flex items-center space-x-1">
                  <Button variant="ghost" size="icon" className="text-white h-7 w-7 hover:bg-purple-700" onClick={() => handleAddItem("unit")}>
                    <Plus size={16} />
                  </Button>
                </div>
              </div>

              <div className="p-2 border-b relative">
                <Input
                  placeholder="Search units..."
                  value={searchUnit}
                  onChange={(e) => setSearchUnit(e.target.value)}
                  className="pl-8"
                />
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>

              <div className="overflow-auto flex-1">
                {selectedTopic ? (
                  filteredUnits.length > 0 ? (
                    filteredUnits.map((unit, index) => (
                      <div key={unit.id}>
                    <div className="flex items-center p-3 border-b hover:bg-gray-50">
                      <div className="flex-shrink-0 mr-3">
                            <div className={`h-10 w-10 rounded-full ${unit.category === 'checklist' ? 'bg-blue-600' : 'bg-green-600'} flex items-center justify-center text-white font-semibold`}>
                              {unit.category === 'checklist' ? 'C' : 'F'}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium flex items-center">
                              {unit.name}
                              {unit.isDefault && <Badge variant="outline" className="ml-2 text-xs">Default</Badge>}
                        </div>
                        <div className="text-xs text-gray-500">
                              {unit.itemCount} Item(s) • Created on: {unit.createdOn}
                          <br />
                              Updated on: {unit.updatedOn}
                        </div>
                      </div>
                      <div className="ml-2 flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-purple-700"
                              onClick={() => handleEditItem(unit)}
                        >
                          <Edit2 size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-purple-700"
                        >
                          <Copy size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-purple-600 hover:text-purple-800"
                              onClick={() => handleCurateClick(unit.id)}
                        >
                          <Wand2 size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-red-600"
                              onClick={() => handleDeleteItem(unit)}
                        >
                          <Trash size={16} />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-gray-500">
                      No units found. Create a new unit in this topic.
                    </div>
                  )
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    Select a topic to view units.
                </div>
              )}
            </div>

            <div className="p-2 mt-auto border-t">
              <Button
                variant="outline"
                  onClick={() => handleAddItem("unit")}
                className="w-full"
                  disabled={!selectedTopic}
              >
                  <Plus size={16} className="mr-1" /> Add Unit
              </Button>
            </div>
          </Card>
          </div>
        )
      )}

      <Dialog open={formOpen} onOpenChange={setFormOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {isEditing ? `Edit ${formType}` : `Add new ${formType}`}
            </DialogTitle>
            <DialogDescription>
              {isEditing
                ? `Update the details for this ${formType}.`
                : `Create a new ${formType}.`}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder={`Enter ${formType} name`}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder={`Enter ${formType} description`}
                  rows={3}
                />
              </div>

              {formType === "unit" && activeTab === "checklistform" && (
                <>
                  <div className="grid gap-2">
                    <Label htmlFor="category">Category</Label>
                    <select
                      id="category"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      defaultValue={currentItem?.type === "unit" ? (currentItem as ChecklistFormUnit).category : "checklist"}
                    >
                      <option value="checklist">Checklist</option>
                      <option value="form">Form</option>
                    </select>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setFormOpen(false)} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
