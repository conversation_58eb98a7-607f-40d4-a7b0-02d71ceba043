
import React, { useState, useMemo, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { User, Search, MoreHorizontal, Upload, Download, Plus } from "lucide-react";
import { Card } from "@/components/ui/card";
import UserFilters from "@/components/users/UserFilters";
import AddUserForm from "@/components/users/AddUserForm";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import apiService, { UserResponse } from "@/services/apiService";



export default function Users() {
  const [activeTab, setActiveTab] = useState("internal");
  const [searchTerm, setSearchTerm] = useState("");
  const [addUserOpen, setAddUserOpen] = useState(false);
  const { toast } = useToast();

  // API state
  const [internalUsers, setInternalUsers] = useState<UserResponse[]>([]);
  const [externalUsers, setExternalUsers] = useState<UserResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load users when component mounts or tab changes
  useEffect(() => {
    loadUsers();
  }, [activeTab]);

  const loadUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      if (activeTab === "internal") {
        const users = await apiService.getUsers('internal');
        setInternalUsers(users);
      } else {
        const users = await apiService.getUsers('external');
        setExternalUsers(users);
      }
    } catch (error) {
      console.error('Error loading users:', error);
      setError('Failed to load users. Please try again.');
      toast({
        title: "Error",
        description: "Failed to load users. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Extract unique values for filters
  const uniqueDivisions = useMemo(() =>
    Array.from(new Set([...internalUsers, ...externalUsers].map(user => user.division))),
    [internalUsers, externalUsers]
  );

  const uniqueDepartments = useMemo(() =>
    Array.from(new Set([...internalUsers, ...externalUsers].map(user => user.department))),
    [internalUsers, externalUsers]
  );

  const uniqueLocations = useMemo(() =>
    Array.from(new Set([...internalUsers, ...externalUsers].map(user => user.location))),
    [internalUsers, externalUsers]
  );

  const uniqueStatuses = useMemo(() =>
    Array.from(new Set([...internalUsers, ...externalUsers].map(user => user.status))),
    [internalUsers, externalUsers]
  );

  // Filter state
  const [filters, setFilters] = useState({
    division: "",
    department: "",
    location: "",
    status: "",
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
  });

  const resetFilters = () => {
    setFilters({
      division: "",
      department: "",
      location: "",
      status: "",
      startDate: undefined,
      endDate: undefined,
    });
  };

  // Filter users based on search term and filters
  const filterUsers = (users: UserResponse[]) => {
    return users.filter((user) => {
      // Search term filter
      const matchesSearch =
        searchTerm === "" ||
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.employeeNo.toLowerCase().includes(searchTerm.toLowerCase());

      // Column filters
      const matchesDivision = filters.division === "" || user.division === filters.division;
      const matchesDepartment = filters.department === "" || user.department === filters.department;
      const matchesLocation = filters.location === "" || user.location === filters.location;
      const matchesStatus = filters.status === "" || user.status === filters.status;

      // Date range filter
      let matchesDateRange = true;
      const userJoiningDate = new Date(user.dateOfJoining);

      if (filters.startDate) {
        matchesDateRange = matchesDateRange && userJoiningDate >= filters.startDate;
      }

      if (filters.endDate) {
        matchesDateRange = matchesDateRange && userJoiningDate <= filters.endDate;
      }

      return matchesSearch && matchesDivision && matchesDepartment && matchesLocation && matchesStatus && matchesDateRange;
    });
  };

  const filteredInternalUsers = filterUsers(internalUsers);
  const filteredExternalUsers = filterUsers(externalUsers);

  const handleAddUser = async (userData: any) => {
    try {
      setIsLoading(true);
      const userType = activeTab as 'internal' | 'external';

      const newUser = await apiService.createUser({
        ...userData,
        type: userType
      });

      // Update local state
      if (userType === 'internal') {
        setInternalUsers(prev => [...prev, newUser]);
      } else {
        setExternalUsers(prev => [...prev, newUser]);
      }

      toast({
        title: "Success",
        description: `${newUser.name} has been added successfully.`,
      });

      setAddUserOpen(false);
    } catch (error) {
      console.error('Error adding user:', error);
      toast({
        title: "Error",
        description: "Failed to add user. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "inactive":
        return "bg-yellow-500";
      case "blocked":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  // Function to handle blocking a user
  const handleBlockUser = async (userId: string, userType: "internal" | "external") => {
    try {
      setIsLoading(true);
      const updatedUser = await apiService.blockUser(userId, userType);

      // Update local state
      if (userType === "internal") {
        setInternalUsers(prev =>
          prev.map(user => user.id === userId ? updatedUser : user)
        );
      } else {
        setExternalUsers(prev =>
          prev.map(user => user.id === userId ? updatedUser : user)
        );
      }

      toast({
        title: "User Blocked",
        description: `${updatedUser.name} has been blocked.`,
      });
    } catch (error) {
      console.error('Error blocking user:', error);
      toast({
        title: "Error",
        description: "Failed to block user. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle activating a user
  const handleActivateUser = async (userId: string, userType: "internal" | "external") => {
    try {
      setIsLoading(true);
      const updatedUser = await apiService.activateUser(userId, userType);

      // Update local state
      if (userType === "internal") {
        setInternalUsers(prev =>
          prev.map(user => user.id === userId ? updatedUser : user)
        );
      } else {
        setExternalUsers(prev =>
          prev.map(user => user.id === userId ? updatedUser : user)
        );
      }

      toast({
        title: "User Activated",
        description: `${updatedUser.name} has been activated.`,
      });
    } catch (error) {
      console.error('Error activating user:', error);
      toast({
        title: "Error",
        description: "Failed to activate user. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Users Management</h1>
      </div>

      <Tabs defaultValue="internal" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="internal">Internal Users</TabsTrigger>
          <TabsTrigger value="external">External Users</TabsTrigger>
        </TabsList>

        <TabsContent value="internal" className="mt-6 space-y-4">
          <Card className="p-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" className="gap-2">
                  <Upload size={16} />
                  Import
                </Button>
                <Button variant="outline" className="gap-2">
                  <Download size={16} />
                  Export
                </Button>
                <Button className="gap-2" onClick={() => setAddUserOpen(true)}>
                  <Plus size={16} />
                  Add User
                </Button>
              </div>
            </div>

            <UserFilters
              divisions={uniqueDivisions}
              departments={uniqueDepartments}
              locations={uniqueLocations}
              statuses={uniqueStatuses}
              filters={filters}
              setFilters={setFilters}
              onResetFilters={resetFilters}
            />

            <div className="rounded-lg border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Employee No.</TableHead>
                    <TableHead className="hidden md:table-cell">Email</TableHead>
                    <TableHead className="hidden lg:table-cell">Phone</TableHead>
                    <TableHead className="hidden lg:table-cell">DOJ</TableHead>
                    <TableHead className="hidden md:table-cell">Division</TableHead>
                    <TableHead className="hidden lg:table-cell">Department</TableHead>
                    <TableHead className="hidden md:table-cell">Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-10">
                        Loading users...
                      </TableCell>
                    </TableRow>
                  ) : filteredInternalUsers.length > 0 ? (
                    filteredInternalUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.employeeNo}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.email}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.phone}</TableCell>
                        <TableCell className="hidden lg:table-cell">{format(new Date(user.dateOfJoining), 'dd/MM/yyyy')}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.division}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.department}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.location}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className="capitalize"
                          >
                            <span className={`mr-1.5 h-2 w-2 rounded-full ${getStatusColor(user.status)}`}></span>
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="View Details">
                              <User className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="Edit User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path><path d="m15 5 4 4"></path></svg>
                            </Button>
                            {user.status === "active" ? (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-orange-600"
                                title="Block User"
                                onClick={() => handleBlockUser(user.id, "internal")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><path d="M9 9h6v6H9z"></path></svg>
                              </Button>
                            ) : (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-green-600"
                                title="Activate User"
                                onClick={() => handleActivateUser(user.id, "internal")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                              </Button>
                            )}
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-red-600" title="Delete User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-10 text-muted-foreground">
                        No users found matching your search criteria.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </Card>

          <AddUserForm
            open={addUserOpen && activeTab === "internal"}
            onOpenChange={setAddUserOpen}
            onAddUser={handleAddUser}
            divisions={uniqueDivisions}
            departments={uniqueDepartments}
            locations={uniqueLocations}
            userType="internal"
          />
        </TabsContent>

        <TabsContent value="external" className="mt-6">
          <Card className="p-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search external users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button className="gap-2" onClick={() => setAddUserOpen(true)}>
                  <Plus size={16} />
                  Add External User
                </Button>
              </div>
            </div>

            <UserFilters
              divisions={uniqueDivisions}
              departments={uniqueDepartments}
              locations={uniqueLocations}
              statuses={uniqueStatuses}
              filters={filters}
              setFilters={setFilters}
              onResetFilters={resetFilters}
            />

            {isLoading ? (
              <div className="text-center py-10">
                Loading external users...
              </div>
            ) : filteredExternalUsers.length > 0 ? (
              <div className="rounded-lg border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>External ID</TableHead>
                      <TableHead className="hidden md:table-cell">Email</TableHead>
                      <TableHead className="hidden lg:table-cell">Phone</TableHead>
                      <TableHead className="hidden lg:table-cell">DOJ</TableHead>
                      <TableHead className="hidden md:table-cell">Division</TableHead>
                      <TableHead className="hidden lg:table-cell">Department</TableHead>
                      <TableHead className="hidden md:table-cell">Location</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredExternalUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.employeeNo}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.email}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.phone}</TableCell>
                        <TableCell className="hidden lg:table-cell">{format(new Date(user.dateOfJoining), 'dd/MM/yyyy')}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.division}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.department}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.location}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className="capitalize"
                          >
                            <span className={`mr-1.5 h-2 w-2 rounded-full ${getStatusColor(user.status)}`}></span>
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="View Details">
                              <User className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="Edit User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path><path d="m15 5 4 4"></path></svg>
                            </Button>
                            {user.status === "active" ? (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-orange-600"
                                title="Block User"
                                onClick={() => handleBlockUser(user.id, "external")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><path d="M9 9h6v6H9z"></path></svg>
                              </Button>
                            ) : (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-green-600"
                                title="Activate User"
                                onClick={() => handleActivateUser(user.id, "external")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                              </Button>
                            )}
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-red-600" title="Delete User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-10">
                <User size={48} className="mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No external users found</h3>
                <p className="text-muted-foreground mb-4">
                  No users match your current search criteria.
                </p>
              </div>
            )}
          </Card>

          <AddUserForm
            open={addUserOpen && activeTab === "external"}
            onOpenChange={setAddUserOpen}
            onAddUser={handleAddUser}
            divisions={uniqueDivisions}
            departments={uniqueDepartments}
            locations={uniqueLocations}
            userType="external"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
