import apiService from '@/services/apiService';

/**
 * Test function to verify content saving and loading functionality
 * This can be called from the browser console for testing
 */
export const testContentApi = async (unitId: string) => {
  try {
    console.log('Testing Content API for unit:', unitId);

    // Sample content items to save
    const sampleContentItems = [
      {
        id: 'item-1',
        type: 'text',
        position: 0,
        content: 'This is a sample text component',
        required: false
      },
      {
        id: 'item-2',
        type: 'image',
        position: 1,
        src: 'https://example.com/image.jpg',
        alt: 'Sample image',
        fileName: 'sample.jpg',
        fileSize: 12345,
        required: false
      },
      {
        id: 'item-3',
        type: 'mcq',
        position: 2,
        question: 'What is the capital of France?',
        options: [
          { id: 'opt-1', text: 'London' },
          { id: 'opt-2', text: 'Paris' },
          { id: 'opt-3', text: 'Berlin' },
          { id: 'opt-4', text: 'Madrid' }
        ],
        allowMultiple: false,
        required: true
      },
      {
        id: 'item-4',
        type: 'attachment',
        position: 3,
        title: 'Sample Document',
        fileName: 'sample-document.pdf',
        fileSize: 2048000,
        src: 'https://example.com/files/sample-document.pdf',
        originalFileName: 'server-file-name.pdf',
        required: false
      }
    ];

    // Test saving content
    console.log('1. Saving content...');
    const saveResponse = await apiService.updateKnowledgeUnitContent(unitId, sampleContentItems);
    console.log('Save response:', saveResponse);

    // Test loading content
    console.log('2. Loading content...');
    const loadResponse = await apiService.getKnowledgeUnit(unitId);
    console.log('Load response:', loadResponse);

    // Verify the content matches
    console.log('3. Verifying content...');
    if (loadResponse.value && loadResponse.value.length === sampleContentItems.length) {
      console.log('✅ Content verification successful!');
      console.log('Saved items:', sampleContentItems.length);
      console.log('Loaded items:', loadResponse.value.length);
    } else {
      console.log('❌ Content verification failed!');
      console.log('Expected items:', sampleContentItems.length);
      console.log('Actual items:', loadResponse.value?.length || 0);
    }

    return { saveResponse, loadResponse };

  } catch (error) {
    console.error('❌ Content API test failed:', error);
    throw error;
  }
};

/**
 * Test file upload functionality
 */
export const testFileUpload = async () => {
  try {
    console.log('Testing File Upload API...');

    // Create a simple test file (1x1 pixel PNG)
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(0, 0, 1, 1);
    }

    // Convert canvas to blob
    const blob = await new Promise<Blob>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob!);
      }, 'image/png');
    });

    // Create file from blob
    const testFile = new File([blob], 'test-image.png', { type: 'image/png' });

    console.log('1. Uploading test file...');
    const uploadResponse = await apiService.uploadFileToFiles(testFile);
    console.log('Upload response:', uploadResponse);

    if (uploadResponse.files && uploadResponse.files.length > 0) {
      const uploadedFile = uploadResponse.files[0];
      console.log('✅ File uploaded successfully!');
      console.log('Original name:', uploadedFile.originalname);
      console.log('File size:', uploadedFile.size);

      // Test getting presigned URL
      console.log('2. Getting presigned URL...');
      const presignedUrl = await apiService.getFileDownloadUrl(uploadedFile.originalname);
      console.log('Presigned URL:', presignedUrl);
      console.log('✅ Presigned URL retrieved successfully!');

      return { uploadResponse, presignedUrl };
    } else {
      throw new Error('No files in upload response');
    }

  } catch (error) {
    console.error('❌ File upload test failed:', error);
    throw error;
  }
};

/**
 * Complete integration test
 */
export const testCompleteIntegration = async (unitId: string) => {
  try {
    console.log('🚀 Starting complete integration test...');

    // Test file upload first
    const fileTest = await testFileUpload();
    console.log('✅ File upload test passed');

    // Test content API
    const contentTest = await testContentApi(unitId);
    console.log('✅ Content API test passed');

    console.log('🎉 All tests passed successfully!');
    return { fileTest, contentTest };

  } catch (error) {
    console.error('❌ Integration test failed:', error);
    throw error;
  }
};

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).testContentApi = testContentApi;
  (window as any).testFileUpload = testFileUpload;
  (window as any).testCompleteIntegration = testCompleteIntegration;
}
